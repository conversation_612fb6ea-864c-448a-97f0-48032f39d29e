import os
import subprocess
import tempfile
import time
from datetime import timedelta
import shutil

# ---------------- 新增导入 -----------------
try:
    from faster_whisper import WhisperModel
except ImportError:
    print("错误: faster-whisper 库未安装。请运行 'pip install faster-whisper'")
    print("你可能还需要 PyTorch: 'pip install torch torchaudio torchvision'")
    WhisperModel = None # 占位，使得脚本在未安装时仍可加载但不执行ASR
# -------------------------------------------

FFMPEG_PATH = "ffmpeg" # 假设 ffmpeg 在系统 PATH 中

# --- (extract_audio_from_video 函数) ---
def extract_audio_from_video(video_path: str, output_audio_path: str = None) -> str | None:
    """
    使用 FFmpeg 从视频文件中提取音频并保存为 WAV 文件。
    """
    if not os.path.exists(video_path):
        print(f"错误: 视频文件不存在 '{video_path}'")
        return None

    if output_audio_path is None:
        base_name = os.path.splitext(os.path.basename(video_path))[0]
        # 获取或创建临时目录
        temp_dir_audio = os.path.join(tempfile.gettempdir(), "video_subtitle_tool_audio")
        os.makedirs(temp_dir_audio, exist_ok=True)
        output_audio_path = os.path.join(temp_dir_audio, f"{base_name}_extracted_audio.wav")
    else:
        # 如果指定了输出路径，确保目录存在
        output_dir = os.path.dirname(output_audio_path)
        if output_dir: # 只有当output_dir不是空（即不是当前目录）时才创建
            os.makedirs(output_dir, exist_ok=True)

    if os.path.exists(output_audio_path):
        print(f"信息: 音频文件 '{output_audio_path}' 已存在，将被覆盖。")

    command = [
        FFMPEG_PATH,
        "-i", video_path,
        "-vn",
        "-acodec", "pcm_s16le",
        "-ar", "16000",
        "-ac", "1",
        "-y", # 自动覆盖输出文件
        output_audio_path
    ]

    print(f"执行 FFmpeg 命令: {' '.join(command)}")

    try:
        process = subprocess.run(command, check=True, capture_output=True, text=True)
        print(f"音频提取成功！已保存到: {output_audio_path}")
        if process.stderr:
            print(f"FFmpeg 输出信息:\n{process.stderr}")
        return output_audio_path
    except subprocess.CalledProcessError as e:
        print(f"FFmpeg 执行错误 (返回码 {e.returncode}):")
        # print(f"Stdout: {e.stdout}") # 通常 ffmpeg 的主要信息在 stderr
        print(f"Stderr: {e.stderr}")
        return None
    except FileNotFoundError:
        print(f"错误: FFmpeg 命令 ('{FFMPEG_PATH}') 未找到。请确保 FFmpeg 已正确安装并添加到系统 PATH 中。")
        return None
    except Exception as e:
        print(f"提取音频时发生未知错误: {e}")
        return None

# --- Whisper ASR 相关函数 ---
_whisper_model_instance = None # 全局缓存模型实例

def get_whisper_model(model_path: str, device: str = "cpu", compute_type: str = "int8"):
    """加载或获取缓存的 Whisper 模型实例"""
    global _whisper_model_instance
    if _whisper_model_instance is None:
        if WhisperModel is None:
            print("错误: 无法加载 Whisper 模型，因为 faster-whisper 未正确导入。")
            return None
        try:
            print(f"[Whisper] 正在加载模型: {model_path} (device: {device}, compute_type: {compute_type})")
            start_time = time.time()
            _whisper_model_instance = WhisperModel(model_path, device=device, compute_type=compute_type)
            load_time = time.time() - start_time
            print(f"[Whisper] 模型加载完成，耗时: {load_time:.2f} 秒。")
        except Exception as e:
            print(f"[Whisper] 加载模型失败: {e}")
            print(f"  请确保模型文件已正确下载并放置在指定路径: '{model_path}'")
            print(f"  模型文件夹应包含 model.bin, config.json, tokenizer.json, vocabulary.txt 等文件。")
            return None
    return _whisper_model_instance

def real_asr_process(audio_file_path: str, model_path: str, language: str = None) -> list:
    """
    使用 Faster Whisper 模型进行语音识别。
    输出带时间戳的词语或句子列表。
    """
    model = get_whisper_model(model_path) # 默认使用 CPU 和 int8
    if not model:
        return []

    print(f"[Whisper] 开始转录音频: {audio_file_path}")
    start_time_transcribe = time.time()
    try:
        segments_iterable, info = model.transcribe(
            audio_file_path,
            beam_size=5,
            word_timestamps=True, # 获取词级别时间戳
            vad_filter=True,
            vad_parameters=dict(min_silence_duration_ms=500), # VAD参数示例
            language=language
        )
        print(f"[Whisper] 检测到语言: {info.language} (置信度: {info.language_probability:.2f})")

        processed_segments = []
        print("[Whisper] 正在处理转录结果...")
        for segment in segments_iterable:
            # 为了简化，我们先用句子/片段级别的时间戳
            # 如果需要更精细的字幕（例如卡拉OK效果或非常精确的断句），可以遍历 segment.words
            # 并根据词语时间戳来组合字幕行。
            # 目前，每个 segment 通常代表一个自然的语音片段。
            processed_segments.append({
                "text": segment.text.strip(),
                "start": segment.start, # 单位已经是秒
                "end": segment.end      # 单位已经是秒
            })
            # 调试输出每个识别到的片段
            # print(f"  片段: [{segment.start:.2f}s -> {segment.end:.2f}s] {segment.text.strip()}")

        transcribe_time = time.time() - start_time_transcribe
        print(f"[Whisper] 音频转录完成，耗时: {transcribe_time:.2f} 秒。")
        return processed_segments

    except Exception as e:
        print(f"[Whisper] 转录过程中发生错误: {e}")
        return []

# --- SRT 生成和 MPV 播放函数 ---
def format_time_for_srt(seconds: float) -> str:
    """将秒数格式化为SRT时间戳格式 (HH:MM:SS,mmm)"""
    td = timedelta(seconds=seconds)
    total_seconds_int = int(td.total_seconds())
    microseconds = td.microseconds
    hours = total_seconds_int // 3600
    minutes = (total_seconds_int % 3600) // 60
    secs = total_seconds_int % 60
    milliseconds = microseconds // 1000
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

def generate_srt_from_segments(segments: list, output_srt_path: str):
    """
    根据 ASR 分段结果（包含 'text', 'start', 'end' 键的字典列表）生成 SRT 文件。
    start 和 end 的单位是秒。
    """
    with open(output_srt_path, "w", encoding="utf-8") as f:
        for i, segment in enumerate(segments):
            if not all(k in segment for k in ("text", "start", "end")):
                print(f"警告: 第 {i+1} 个片段数据不完整，跳过。片段数据: {segment}")
                continue

            start_time_str = format_time_for_srt(segment["start"])
            end_time_str = format_time_for_srt(segment["end"])

            f.write(f"{i + 1}\n")
            f.write(f"{start_time_str} --> {end_time_str}\n")
            f.write(f"{segment['text']}\n\n") # 每个字幕条目后需要一个空行
    print(f"[SRT] 已生成SRT文件: {output_srt_path}")

def play_video_with_subtitles_mpv(video_path: str, srt_path: str, mpv_executable_path: str = "mpv"):
    """使用 MPV 播放视频并加载字幕文件"""
    if not os.path.exists(mpv_executable_path) and not shutil.which(mpv_executable_path):
        print(f"错误: MPV 命令 ('{mpv_executable_path}') 未找到或不可执行。请确保 MPV 已正确安装并添加到系统 PATH 中，或在脚本中提供正确路径。")
        return

    command = [
        mpv_executable_path,
        video_path,
        f"--sub-file={srt_path}"
    ]
    print(f"执行 MPV 命令: {' '.join(command)}")
    try:
        subprocess.run(command)
        print("MPV 播放结束或已关闭。")
    except FileNotFoundError:
        print(f"错误: MPV 命令 ('{mpv_executable_path}') 未找到。")
    except Exception as e:
        print(f"播放视频时发生错误: {e}")


if __name__ == "__main__":
    # 获取脚本所在的src目录的父目录，即项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    default_test_video = os.path.join(project_root, "test_video.mp4") # 假设测试视频在项目根目录
    mpv_executable = "mpv" # 假设 mpv 在 PATH 中

    # --- 配置 Whisper 模型路径 ---
    # 将 'small' 替换为你下载的模型文件夹名称 (如 'base', 'medium', 'large-v3')
    whisper_model_name = "small"  # <<<--- 确保这里与你下载的模型文件夹名一致
    path_to_whisper_model_dir = os.path.join(project_root, "models", "whisper_models")
    path_to_specific_model = os.path.join(path_to_whisper_model_dir, whisper_model_name)
    # --- --------------------- ---

    video_input_path = input(f"请输入要测试的视频文件路径 (默认: {default_test_video}): ")
    if not video_input_path:
        video_input_path = default_test_video

    if not os.path.exists(video_input_path):
        print(f"错误: 视频文件 '{video_input_path}' 不存在。")
        exit()

    if WhisperModel is None:
        print("错误: 无法运行，因为 faster-whisper 未成功导入。请检查安装。")
        exit()

    if not os.path.isdir(path_to_specific_model):
        print(f"错误: Whisper 模型路径 '{path_to_specific_model}' 不存在或不是一个目录。")
        print(f"  请确保已下载 '{whisper_model_name}' 模型并将其放置在: '{path_to_whisper_model_dir}/' 下。")
        print(f"  模型文件夹 '{whisper_model_name}' 应直接位于 '{path_to_whisper_model_dir}' 目录下，")
        print(f"  并且其中包含 model.bin, config.json, tokenizer.json, vocabulary.txt 等文件。")
        exit()


    base_name = os.path.splitext(os.path.basename(video_input_path))[0]
    # 将生成的SRT文件也保存到项目根目录的 output 文件夹中
    output_dir_project = os.path.join(project_root, "output")
    os.makedirs(output_dir_project, exist_ok=True) # 确保output目录存在
    srt_output_path_project = os.path.join(output_dir_project, f"{base_name}_whisper_generated.srt")

    # 音频文件仍然可以放在临时目录，因为它只是中间产物
    with tempfile.TemporaryDirectory(prefix="video_subtitle_tool_") as temp_dir:
        print(f"临时工作目录 (用于音频): {temp_dir}")
        audio_output_path = os.path.join(temp_dir, f"{base_name}_extracted_audio.wav")

        # 1. 提取音频
        print("\n--- 步骤 1: 提取音频 ---")
        extracted_audio_file = extract_audio_from_video(video_input_path, audio_output_path)
        if not extracted_audio_file:
            print("无法提取音频，程序终止。")
            exit()

        # 2. 语音识别 (使用 Faster Whisper)
        print("\n--- 步骤 2: 语音识别 (使用 Faster Whisper) ---")
        # 你可以指定语言代码，例如 language="zh" 代表中文，language="en" 代表英文
        # 如果不指定 (None)，模型会自动检测。对于 macOS M芯片，可以尝试 device="mps"
        asr_segments = real_asr_process(
            extracted_audio_file,
            model_path=path_to_specific_model, # 传递模型文件夹的路径
            language=None # 让模型自动检测语言
        )
        if not asr_segments:
            print("ASR 处理失败，程序终止。")
            exit()

        # 3. 生成 SRT 字幕文件
        print("\n--- 步骤 3: 生成 SRT 字幕文件 ---")
        generate_srt_from_segments(asr_segments, srt_output_path_project)

        # 4. 使用 MPV 播放视频和字幕
        print("\n--- 步骤 4: 播放视频与字幕 ---")
        play_video_with_subtitles_mpv(video_input_path, srt_output_path_project, mpv_executable_path=mpv_executable)

        print("\n--- 原型流程 (使用真实ASR) 完成 ---")
        print(f"生成的SRT字幕文件保存在: {srt_output_path_project}")